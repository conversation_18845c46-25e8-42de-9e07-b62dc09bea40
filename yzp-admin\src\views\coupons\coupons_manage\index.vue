<script setup lang="ts">
import { ref, onMounted, onUnmounted } from "vue";
import TableDetails from "./components/index.vue";
import { getInfoList } from "@/api/info_query/index";
import { useRoute } from "vue-router";
import { formatTimestamp } from "@/utils/dateFormat";
import type { ComponentSize } from "element-plus";

const form = ref<any>({});
const detailsDialogVisible = ref(false);
const isRightType = ref("");
const loading = ref(false);

let formQuery = [
  {
    type: "input",
    key: "positionName",
    label: "岗位名称"
  },
  {
    type: "input",
    key: "createUserName",
    label: "招聘者"
  },
  {
    type: "select",
    key: "manualInspectionStatus",
    label: "视检状态"
  },
  {
    type: "datetime",
    key: "dates",
    label: "提交时间"
  }
];
interface Cms {
  property: string;
  label: string;
  width: string;
}
const tableCms: Cms[] = [
  {
    property: "positionName",
    label: "岗位名称",
    width: ""
  },
  {
    property: "jobType",
    label: "工作类型",
    width: ""
  },
  {
    property: "createUserName",
    label: "招聘者",
    width: ""
  },
  {
    property: "createTime",
    label: "提交时间",
    width: ""
  },
  {
    property: "manualInspectionStatus",
    label: "视检状态",
    width: ""
  }
];
const tableData = ref<any[]>([]);

const currentPage = ref(1);
const pageSize = ref(10);
const size = ref<ComponentSize>("default");
const background = ref(false);
const disabled = ref(false);
const total = ref(0);
const currentRow = ref<any>({});

const optionsConfig = ref<any[]>([
  {
    label: "待审核",
    value: 0
  },
  {
    label: "通过",
    value: 1
  },
  {
    label: "驳回",
    value: 2
  }
]);

// 分页接口入参
const params = ref({
  entity: {
    positionInfoSearchDTO: {
      endTime: "",
      startTime: "",
      manualInspectionStatus: "",
      createUserName: "",
      positionName: ""
    },
    type: 6 //岗位信息查询type = 6
  },
  orderBy: {},
  page: 1,
  size: 10
});

const handleSizeChange = val => {
  pageSize.value = val;
  params.value.size = val;
  params.value.page = 1;
  getInfoListData();
};
const handleCurrentChange = val => {
  currentPage.value = val;
  params.value.page = val;
  getInfoListData();
};

const headerHeight: number = 420;
const tableHeight = ref(window.innerHeight - headerHeight); // 计算高度，headerHeight是你页面顶部栏的高度
function resizeTableHeight() {
  tableHeight.value = window.innerHeight - headerHeight; // 更新表格高度
}

function handleDetails(item) {
  currentRow.value = item.row;
  detailsDialogVisible.value = true;
}
const handleCancelBtn = () => {
  isRightType.value = "";
  detailsDialogVisible.value = false;
  getInfoListData();
};

// 搜索
const handleSearch = () => {
  // 只处理用户输入的字段
  if (form.value.dates && form.value.dates.length === 2) {
    params.value.entity.positionInfoSearchDTO.startTime = form.value.dates[0];
    params.value.entity.positionInfoSearchDTO.endTime = form.value.dates[1];
  } else {
    delete params.value.entity.positionInfoSearchDTO.startTime;
    delete params.value.entity.positionInfoSearchDTO.endTime;
  }

  params.value.entity.positionInfoSearchDTO.manualInspectionStatus =
    form.value.manualInspectionStatus;
  params.value.entity.positionInfoSearchDTO.positionName =
    form.value.positionName;
  params.value.entity.positionInfoSearchDTO.createUserName =
    form.value.createUserName;
  // status 不变
  getInfoListData();
};
// 重置
const handleReset = () => {
  form.value = {};
  params.value = {
    entity: {
      positionInfoSearchDTO: {
        endTime: "",
        startTime: "",
        manualInspectionStatus: "",
        createUserName: "",
        positionName: ""
      },
      type: 6 //岗位信息查询type = 6
    },
    orderBy: {},
    page: 1,
    size: 10
  };
  getInfoListData();
};

const getInfoListData = async () => {
  loading.value = true;
  try {
    const res: any = await getInfoList(params.value);
    if (res.code === 0) {
      total.value = res.data.total;
      tableData.value = res.data.list;
    }
  } catch (error) {
    console.log(error);
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  getInfoListData();
  window.addEventListener("resize", resizeTableHeight);
});
onUnmounted(() => {
  window.removeEventListener("resize", resizeTableHeight);
});
</script>

<template>
  <div>
    <el-card shadow="never">
      <div class="table-header-flex">
        <el-form :inline="true" :model="form" class="table-header-form">
          <el-form-item
            v-for="(item, index) in formQuery"
            :key="index"
            :label="item.label"
            class="form-item"
          >
            <el-input
              v-if="item.type === 'input'"
              v-model="form[item.key]"
              size="large"
              :placeholder="'请输入' + item.label"
              style="width: 180px"
              clearable
            />
            <el-select
              v-else-if="item.type === 'select'"
              v-model="form[item.key]"
              size="large"
              :placeholder="'请选择' + item.label"
              style="width: 180px"
              clearable
            >
              <el-option
                v-for="option in optionsConfig"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
            <el-date-picker
              v-else-if="item.type === 'datetime'"
              v-model="form[item.key]"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              size="large"
              style="width: 380px"
              value-format="YYYY-MM-DD HH:mm:ss"
              clearable
            />
          </el-form-item>
        </el-form>
        <div class="form-btns">
          <el-button size="large" type="primary" @click="handleSearch"
            >搜索</el-button
          >
          <el-button
            size="large"
            type="info"
            style="background-color: #b4c4d1; color: #ffffff; border: #b4c4d1"
            @click="handleReset"
            >重置</el-button
          >
        </div>
      </div>
    </el-card>
    <el-card shadow="never" style="margin-top: 15px; padding: 5px 15px">
      <el-table
        ref="tableContainer"
        :data="tableData"
        :loading="loading"
        element-loading-text="数据加载中..."
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(255, 255, 255, 0.9)"
        element-loading-svg-view-box="-10, -10, 50, 50"
        style="width: 100%"
        :height="tableHeight"
      >
        <!-- <el-table-column type="selection" width="60" /> -->
        <el-table-column
          v-for="(config, index) in tableCms"
          :key="index"
          :width="config.width"
          :label="config.label"
          show-overflow-tooltip
        >
          <template #default="scope">
            <span v-if="config.property === 'createTime'">
              {{ formatTimestamp(scope.row[config.property]) }}
            </span>

            <span
              v-else-if="config.property === 'manualInspectionStatus'"
              :style="{
                color:
                  scope.row[config.property] === 0
                    ? ''
                    : scope.row[config.property] === 1
                      ? '#67C23A'
                      : '#fb5451'
              }"
            >
              {{
                scope.row[config.property] === 0
                  ? "待审核"
                  : scope.row[config.property] === 1
                    ? "通过"
                    : "驳回"
              }}
            </span>

            <span v-else-if="config.property === 'jobType'">
              {{
                scope.row[config.property] === 1
                  ? "全职"
                  : scope.row[config.property] === 2
                    ? "兼职"
                    : "实习"
              }}
            </span>

            <span v-else>
              {{ scope.row[config.property] }}
            </span>
          </template>
        </el-table-column>

        <el-table-column fixed="right" label="操作" min-width="120">
          <template #default="scope">
            <el-button
              link
              type="primary"
              style="color: #279efb"
              @click="handleDetails(scope)"
            >
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div style="display: flex; justify-content: flex-end; margin-top: 20px">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :size="size"
          :disabled="disabled"
          :background="background"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
    <TableDetails
      v-model:dialogVisible="detailsDialogVisible"
      :currentRow="currentRow"
      @cancelBtn="handleCancelBtn"
    />
  </div>
</template>
<style scoped lang="scss">
.billsplit-row {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  height: 60px;
  line-height: 60px;
}
.table-header {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  padding: 5px 20px;
  font-size: 14px;
  button {
    width: 74px !important;
    height: 40px !important;
  }
}
.table-header-row {
  display: flex;
  align-items: center;
  padding: 10px 20px;
  gap: 24px;
  background: #fff;
}
.form-item {
  display: flex;
  align-items: center;
  gap: 8px;
}
.table-header-flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
}
.table-header-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  flex: 1;
}
.form-item {
  margin-bottom: 0;
  display: flex;
  align-items: center;
}
.form-btns {
  display: flex;
  align-items: center;
  margin-left: 24px;
  gap: 12px;
}
</style>
